/**
 * React hook for managing comments in the collaborative editor
 */

import { useEffect, useCallback, useRef } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { commentPluginHelpers, type CommentData } from "../lib/commentPlugin";
import { toast } from "sonner";

interface UseCommentsProps {
  documentId: Id<"documents">;
  editor: any; // BlockNote/ProseMirror editor instance
  isReadOnly?: boolean;
}

export function useComments({
  documentId,
  editor,
  isReadOnly = false
}: UseCommentsProps) {
  const comments = useQuery(api.comments.getDocumentComments, { documentId });
  const createCommentMutation = useMutation(api.comments.createComment);
  const updateCommentMutation = useMutation(api.comments.updateComment);
  const deleteCommentMutation = useMutation(api.comments.deleteComment);
  const createReplyMutation = useMutation(api.comments.createReply);
  const updateReplyMutation = useMutation(api.comments.updateReply);
  const deleteReplyMutation = useMutation(api.comments.deleteReply);
  const resolveCommentMutation = useMutation(api.comments.resolveComment);
  
  const lastCommentsUpdate = useRef<number>(0);

  // Update plugin state when comments change
  useEffect(() => {
    if (!editor?.prosemirrorView || !comments) return;
    
    // Prevent unnecessary updates
    const commentsHash = JSON.stringify(comments.map(c => ({ 
      id: c._id, 
      content: c.content, 
      isResolved: c.isResolved,
      timestamp: c.timestamp 
    })));
    const currentTime = Date.now();
    
    if (currentTime - lastCommentsUpdate.current < 100) return;
    lastCommentsUpdate.current = currentTime;
    
    // Transform comments to plugin format
    const pluginComments: CommentData[] = comments.map(comment => ({
      id: comment._id,
      documentId: comment.documentId,
      userId: comment.userId,
      content: comment.content,
      position: comment.position,
      selection: comment.selection,
      selectedText: comment.selectedText,
      isResolved: comment.isResolved,
      timestamp: comment.timestamp,
      user: comment.user,
      replies: comment.replies,
    }));
    
    commentPluginHelpers.setComments(editor.prosemirrorView, pluginComments);
  }, [editor, comments]);

  /**
   * Create a new comment
   */
  const createComment = useCallback(async (content: string) => {
    if (!editor?.prosemirrorView || isReadOnly) return null;
    
    const selectionInfo = commentPluginHelpers.getSelectionInfo(editor.prosemirrorView);
    if (!selectionInfo) {
      toast.error("Please select text to comment on");
      return null;
    }
    
    try {
      const commentId = await createCommentMutation({
        documentId,
        content,
        position: selectionInfo.position,
        selection: {
          from: selectionInfo.from,
          to: selectionInfo.to,
        },
        selectedText: selectionInfo.selectedText,
      });
      
      toast.success("Comment added successfully");
      return commentId;
    } catch (error) {
      toast.error("Failed to add comment");
      console.error("Error creating comment:", error);
      return null;
    }
  }, [editor, documentId, createCommentMutation, isReadOnly]);

  /**
   * Update an existing comment
   */
  const updateComment = useCallback(async (commentId: Id<"comments">, content: string) => {
    try {
      await updateCommentMutation({
        commentId,
        content,
      });
      
      toast.success("Comment updated successfully");
      return true;
    } catch (error) {
      toast.error("Failed to update comment");
      console.error("Error updating comment:", error);
      return false;
    }
  }, [updateCommentMutation]);

  /**
   * Delete a comment
   */
  const deleteComment = useCallback(async (commentId: Id<"comments">) => {
    try {
      await deleteCommentMutation({ commentId });
      
      toast.success("Comment deleted successfully");
      return true;
    } catch (error) {
      toast.error("Failed to delete comment");
      console.error("Error deleting comment:", error);
      return false;
    }
  }, [deleteCommentMutation]);

  /**
   * Create a reply to a comment
   */
  const createReply = useCallback(async (commentId: Id<"comments">, content: string) => {
    try {
      const replyId = await createReplyMutation({
        commentId,
        content,
      });
      
      toast.success("Reply added successfully");
      return replyId;
    } catch (error) {
      toast.error("Failed to add reply");
      console.error("Error creating reply:", error);
      return null;
    }
  }, [createReplyMutation]);

  /**
   * Update a reply
   */
  const updateReply = useCallback(async (replyId: Id<"commentReplies">, content: string) => {
    try {
      await updateReplyMutation({
        replyId,
        content,
      });
      
      toast.success("Reply updated successfully");
      return true;
    } catch (error) {
      toast.error("Failed to update reply");
      console.error("Error updating reply:", error);
      return false;
    }
  }, [updateReplyMutation]);

  /**
   * Delete a reply
   */
  const deleteReply = useCallback(async (replyId: Id<"commentReplies">) => {
    try {
      await deleteReplyMutation({ replyId });
      
      toast.success("Reply deleted successfully");
      return true;
    } catch (error) {
      toast.error("Failed to delete reply");
      console.error("Error deleting reply:", error);
      return false;
    }
  }, [deleteReplyMutation]);

  /**
   * Resolve or unresolve a comment
   */
  const resolveComment = useCallback(async (commentId: Id<"comments">, isResolved: boolean) => {
    if (isReadOnly) {
      toast.error("You don't have permission to resolve comments");
      return false;
    }
    
    try {
      await resolveCommentMutation({
        commentId,
        isResolved,
      });
      
      toast.success(isResolved ? "Comment resolved" : "Comment reopened");
      return true;
    } catch (error) {
      toast.error("Failed to update comment status");
      console.error("Error resolving comment:", error);
      return false;
    }
  }, [resolveCommentMutation, isReadOnly]);

  /**
   * Scroll to a comment in the sidebar
   */
  const scrollToComment = useCallback((commentId: string) => {
    setTimeout(() => {
      const commentElement = document.querySelector(`[data-testid="comment-thread"][data-comment-id="${commentId}"]`);
      if (commentElement) {
        commentElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 100);
  }, []);

  /**
   * Select a comment (highlight it and show in sidebar)
   */
  const selectComment = useCallback((commentId: string) => {
    if (!editor?.prosemirrorView) return;

    commentPluginHelpers.selectComment(editor.prosemirrorView, commentId);
    commentPluginHelpers.setSidebar(editor.prosemirrorView, true);
    scrollToComment(commentId);
  }, [editor, scrollToComment]);

  // Listen for comment selection events from the editor
  useEffect(() => {
    const handleCommentSelected = (event: CustomEvent) => {
      const { commentId } = event.detail;
      scrollToComment(commentId);
    };

    window.addEventListener('comment-selected', handleCommentSelected as EventListener);
    return () => {
      window.removeEventListener('comment-selected', handleCommentSelected as EventListener);
    };
  }, [scrollToComment]);

  /**
   * Toggle comment sidebar visibility
   */
  const toggleSidebar = useCallback(() => {
    if (!editor?.prosemirrorView) return;
    
    commentPluginHelpers.toggleSidebar(editor.prosemirrorView);
  }, [editor]);

  /**
   * Get current plugin state
   */
  const getPluginState = useCallback(() => {
    if (!editor?.prosemirrorView) return null;
    
    return commentPluginHelpers.getState(editor.prosemirrorView);
  }, [editor]);

  return {
    comments: comments || [],
    createComment,
    updateComment,
    deleteComment,
    createReply,
    updateReply,
    deleteReply,
    resolveComment,
    selectComment,
    toggleSidebar,
    getPluginState,
    isLoading: comments === undefined,
  };
}
