/**
 * ProseMirror plugin for handling comments and comment decorations
 */

import { Plugin, Plugin<PERSON>ey } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";
import { getUserColor } from "./userColors";

export interface CommentData {
  id: string;
  documentId: string;
  userId: string;
  content: string;
  position: number;
  selection: {
    from: number;
    to: number;
  };
  selectedText: string;
  isResolved?: boolean;
  timestamp: number;
  user?: {
    _id: string;
    name?: string;
    email: string;
    image?: string;
  };
  replies?: CommentReply[];
}

export interface CommentReply {
  _id: string;
  commentId: string;
  userId: string;
  content: string;
  timestamp: number;
  user?: {
    _id: string;
    name?: string;
    email: string;
    image?: string;
  };
}

export interface CommentPluginState {
  comments: CommentData[];
  decorations: DecorationSet;
  selectedCommentId?: string;
  showSidebar: boolean;
}

export const commentPluginKey = new PluginKey<CommentPluginState>("comments");

/**
 * Create a decoration for a comment with user-specific colors and selection states
 */
function createCommentDecoration(comment: CommentData, isSelected: boolean = false, commentCount: number = 1) {
  const userColor = getUserColor(comment.userId);
  const userName = comment.user?.name || comment.user?.email || "Unknown User";

  // Build CSS classes
  const baseClasses = ["comment-highlight"];

  if (comment.isResolved) {
    baseClasses.push("comment-resolved");
  } else {
    baseClasses.push("comment-active");
  }

  if (isSelected) {
    baseClasses.push("comment-selected");
  }

  if (commentCount > 1) {
    baseClasses.push("comment-multiple");
  }

  // Create inline styles for user-specific colors
  const backgroundColor = comment.isResolved
    ? `rgba(${userColor.rgb}, 0.1)`
    : `rgba(${userColor.rgb}, ${isSelected ? 0.25 : 0.15})`;

  const borderColor = comment.isResolved
    ? `rgba(${userColor.rgb}, 0.2)`
    : `rgba(${userColor.rgb}, ${isSelected ? 0.6 : 0.3})`;

  const style = `
    background-color: ${backgroundColor};
    border: 1px solid ${borderColor};
    ${isSelected ? 'box-shadow: 0 0 0 2px rgba(' + userColor.rgb + ', 0.3);' : ''}
  `;

  return Decoration.inline(
    comment.selection.from,
    comment.selection.to,
    {
      class: baseClasses.join(" "),
      style: style.trim(),
      "data-comment-id": comment.id,
      "data-user-id": comment.userId,
      "data-comment-count": commentCount.toString(),
      "data-from": comment.selection.from.toString(),
      "data-to": comment.selection.to.toString(),
      title: `Comment by ${userName}: ${comment.content.substring(0, 100)}${comment.content.length > 100 ? '...' : ''}`,
    },
    {
      inclusiveStart: false,
      inclusiveEnd: false,
    }
  );
}

/**
 * Update decorations based on current comments with selection state and multiple comment handling
 */
function updateDecorations(doc: any, comments: CommentData[], selectedCommentId?: string): DecorationSet {
  const decorations: Decoration[] = [];

  // Group comments by their text selection range to handle multiple comments on same text
  const commentGroups = new Map<string, CommentData[]>();

  comments.forEach((comment) => {
    // Validate that the comment position is still valid in the document
    if (comment.selection.from >= 0 &&
        comment.selection.to <= doc.content.size &&
        comment.selection.from < comment.selection.to) {

      const rangeKey = `${comment.selection.from}-${comment.selection.to}`;
      if (!commentGroups.has(rangeKey)) {
        commentGroups.set(rangeKey, []);
      }
      commentGroups.get(rangeKey)!.push(comment);
    }
  });

  // Create decorations for each group
  commentGroups.forEach((groupComments, rangeKey) => {
    // Sort comments by timestamp (oldest first)
    groupComments.sort((a, b) => a.timestamp - b.timestamp);

    // Check if any comment in this group is selected
    const hasSelectedComment = groupComments.some(c => c.id === selectedCommentId);

    // For multiple comments on same text, we'll create a decoration for the primary comment
    // but include information about all comments
    const primaryComment = groupComments[0];
    const commentCount = groupComments.length;

    decorations.push(createCommentDecoration(
      primaryComment,
      hasSelectedComment,
      commentCount
    ));
  });

  return DecorationSet.create(doc, decorations);
}

/**
 * Create the comment plugin
 */
export function createCommentPlugin() {
  return new Plugin<CommentPluginState>({
    key: commentPluginKey,
    
    state: {
      init(): CommentPluginState {
        return {
          comments: [],
          decorations: DecorationSet.empty,
          showSidebar: false,
        };
      },
      
      apply(tr, pluginState, oldState, newState) {
        let { comments, decorations, selectedCommentId, showSidebar } = pluginState;
        
        // Handle document changes - update decorations
        if (tr.docChanged) {
          decorations = updateDecorations(newState.doc, comments, selectedCommentId);
        }
        
        // Handle comment-related transactions
        const commentMeta = tr.getMeta(commentPluginKey);
        if (commentMeta) {
          switch (commentMeta.type) {
            case "setComments":
              comments = commentMeta.comments;
              decorations = updateDecorations(newState.doc, comments, selectedCommentId);
              break;

            case "addComment":
              comments = [...comments, commentMeta.comment];
              decorations = updateDecorations(newState.doc, comments, selectedCommentId);
              break;

            case "updateComment":
              comments = comments.map(c =>
                c.id === commentMeta.comment.id ? commentMeta.comment : c
              );
              decorations = updateDecorations(newState.doc, comments, selectedCommentId);
              break;

            case "deleteComment":
              comments = comments.filter(c => c.id !== commentMeta.commentId);
              decorations = updateDecorations(newState.doc, comments, selectedCommentId);
              if (selectedCommentId === commentMeta.commentId) {
                selectedCommentId = undefined;
              }
              break;

            case "selectComment":
              selectedCommentId = commentMeta.commentId;
              // Update decorations to reflect the new selection
              decorations = updateDecorations(newState.doc, comments, selectedCommentId);
              break;
              
            case "toggleSidebar":
              showSidebar = !showSidebar;
              break;
              
            case "setSidebar":
              showSidebar = commentMeta.show;
              break;
          }
        }
        
        return {
          comments,
          decorations,
          selectedCommentId,
          showSidebar,
        };
      },
    },
    
    props: {
      decorations(state) {
        return commentPluginKey.getState(state)?.decorations;
      },
      
      handleDOMEvents: {
        // Handle clicks on comment decorations
        click(view, event) {
          const target = event.target as HTMLElement;
          const commentElement = target.closest('[data-comment-id]');

          if (commentElement) {
            const commentId = commentElement.getAttribute('data-comment-id');
            const commentCount = parseInt(commentElement.getAttribute('data-comment-count') || '1');

            if (commentId) {
              // If there are multiple comments on the same text, cycle through them
              const state = commentPluginKey.getState(view.state);
              const currentSelected = state?.selectedCommentId;

              if (commentCount > 1 && currentSelected === commentId) {
                // Find all comments at this position and select the next one
                const commentsAtPosition = state?.comments.filter(c =>
                  c.selection.from === parseInt(commentElement.getAttribute('data-from') || '0') &&
                  c.selection.to === parseInt(commentElement.getAttribute('data-to') || '0')
                ) || [];

                if (commentsAtPosition.length > 1) {
                  const currentIndex = commentsAtPosition.findIndex(c => c.id === currentSelected);
                  const nextIndex = (currentIndex + 1) % commentsAtPosition.length;
                  const nextCommentId = commentsAtPosition[nextIndex].id;

                  const tr = view.state.tr.setMeta(commentPluginKey, {
                    type: "selectComment",
                    commentId: nextCommentId,
                  });
                  view.dispatch(tr);

                  // Trigger custom event for sidebar scrolling
                  window.dispatchEvent(new CustomEvent('comment-selected', {
                    detail: { commentId: nextCommentId }
                  }));

                  return true;
                }
              }

              // Select the comment and show sidebar
              const tr = view.state.tr.setMeta(commentPluginKey, {
                type: "selectComment",
                commentId,
              });
              view.dispatch(tr);

              // Also ensure sidebar is visible
              const tr2 = view.state.tr.setMeta(commentPluginKey, {
                type: "setSidebar",
                show: true,
              });
              view.dispatch(tr2);

              // Trigger custom event for sidebar scrolling
              window.dispatchEvent(new CustomEvent('comment-selected', {
                detail: { commentId }
              }));

              return true;
            }
          }

          return false;
        },
      },
    },
  });
}

/**
 * Helper functions for interacting with the comment plugin
 */
export const commentPluginHelpers = {
  /**
   * Set all comments for the document
   */
  setComments: (view: any, comments: CommentData[]) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "setComments",
      comments,
    });
    view.dispatch(tr);
  },
  
  /**
   * Add a new comment
   */
  addComment: (view: any, comment: CommentData) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "addComment",
      comment,
    });
    view.dispatch(tr);
  },
  
  /**
   * Update an existing comment
   */
  updateComment: (view: any, comment: CommentData) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "updateComment",
      comment,
    });
    view.dispatch(tr);
  },
  
  /**
   * Delete a comment
   */
  deleteComment: (view: any, commentId: string) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "deleteComment",
      commentId,
    });
    view.dispatch(tr);
  },
  
  /**
   * Select a comment
   */
  selectComment: (view: any, commentId: string) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "selectComment",
      commentId,
    });
    view.dispatch(tr);
  },
  
  /**
   * Toggle comment sidebar visibility
   */
  toggleSidebar: (view: any) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "toggleSidebar",
    });
    view.dispatch(tr);
  },
  
  /**
   * Set sidebar visibility
   */
  setSidebar: (view: any, show: boolean) => {
    const tr = view.state.tr.setMeta(commentPluginKey, {
      type: "setSidebar",
      show,
    });
    view.dispatch(tr);
  },
  
  /**
   * Get current plugin state
   */
  getState: (view: any): CommentPluginState | undefined => {
    return commentPluginKey.getState(view.state);
  },
  
  /**
   * Get current selection info for creating a comment
   */
  getSelectionInfo: (view: any) => {
    const { from, to } = view.state.selection;
    if (from === to) return null; // No selection

    const selectedText = view.state.doc.textBetween(from, to);
    return {
      from,
      to,
      selectedText,
      position: from, // Use start position as anchor
    };
  },

  /**
   * Get the currently selected comment ID
   */
  getSelectedCommentId: (view: any): string | undefined => {
    const state = commentPluginKey.getState(view.state);
    return state?.selectedCommentId;
  },

  /**
   * Get comments at a specific position
   */
  getCommentsAtPosition: (view: any, position: number): CommentData[] => {
    const state = commentPluginKey.getState(view.state);
    if (!state) return [];

    return state.comments.filter(comment =>
      position >= comment.selection.from && position <= comment.selection.to
    );
  },
};
