# Test info

- Name: Commenting System Integration >> should show Add Comment button only when text is selected
- Location: /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/commenting.integration.test.ts:55:3

# Error details

```
Error: page.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('button:has-text("New Document")')

    at /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/commenting.integration.test.ts:57:16
```

# Page snapshot

```yaml
- banner:
  - heading "Collaborative Editor" [level=2]
- main:
  - heading "Collaborative Editor" [level=1]
  - paragraph: Create and edit documents together in real-time
  - textbox "Email"
  - textbox "Password"
  - button "Sign in"
  - text: Don't have an account?
  - button "Sign up instead"
  - separator
  - text: or
  - separator
  - button "Sign in anonymously"
- region "Notifications alt+T"
```

# Test source

```ts
   1 | /**
   2 |  * Integration tests for the commenting system
   3 |  * Tests end-to-end commenting workflows using Playwright
   4 |  */
   5 |
   6 | import { test, expect } from '@playwright/test';
   7 |
   8 | test.describe('Commenting System Integration', () => {
   9 |   test.beforeEach(async ({ page }) => {
   10 |     // Navigate to the application
   11 |     await page.goto('/');
   12 |     
   13 |     // Wait for the application to load
   14 |     await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
   15 |   });
   16 |
   17 |   test('should create a comment via selection toolbar button', async ({ page }) => {
   18 |     // Create a new document first
   19 |     await page.click('button:has-text("New Document")');
   20 |     await page.waitForSelector('.prose', { timeout: 5000 });
   21 |
   22 |     // Add some text to the editor
   23 |     const editor = page.locator('.prose');
   24 |     await editor.click();
   25 |     await page.keyboard.type('This is a test document with some content to comment on.');
   26 |
   27 |     // Select text for commenting
   28 |     await page.keyboard.press('Control+a'); // Select all text
   29 |
   30 |     // Wait for the formatting toolbar to appear with the "Add Comment" button
   31 |     await page.waitForSelector('button:has-text("Add Comment")', { timeout: 3000 });
   32 |
   33 |     // Verify the button is in the formatting toolbar (floating selection toolbar)
   34 |     const addCommentButton = page.locator('button:has-text("Add Comment")');
   35 |     await expect(addCommentButton).toBeVisible();
   36 |
   37 |     // Click "Add Comment" button in the selection toolbar
   38 |     await addCommentButton.click();
   39 |
   40 |     // Wait for comment popover to appear
   41 |     await page.waitForSelector('[data-testid="comment-popover"]', { timeout: 3000 });
   42 |
   43 |     // Type comment content
   44 |     const commentInput = page.locator('textarea[placeholder*="comment"]');
   45 |     await commentInput.fill('This is a test comment on the selected text.');
   46 |
   47 |     // Submit the comment
   48 |     await page.click('button:has-text("Add Comment")');
   49 |
   50 |     // Verify comment was created
   51 |     await page.waitForSelector('.comment-highlight', { timeout: 3000 });
   52 |     expect(await page.locator('.comment-highlight').count()).toBe(1);
   53 |   });
   54 |
   55 |   test('should show Add Comment button only when text is selected', async ({ page }) => {
   56 |     // Create a new document first
>  57 |     await page.click('button:has-text("New Document")');
      |                ^ Error: page.click: Test timeout of 30000ms exceeded.
   58 |     await page.waitForSelector('.prose', { timeout: 5000 });
   59 |
   60 |     // Add some text to the editor
   61 |     const editor = page.locator('.prose');
   62 |     await editor.click();
   63 |     await page.keyboard.type('This is a test document with some content.');
   64 |
   65 |     // Initially, no text is selected, so formatting toolbar should not be visible
   66 |     const addCommentButton = page.locator('button:has-text("Add Comment")');
   67 |     await expect(addCommentButton).not.toBeVisible();
   68 |
   69 |     // Select text
   70 |     await page.keyboard.press('Control+a');
   71 |
   72 |     // Now the formatting toolbar should appear with the Add Comment button
   73 |     await expect(addCommentButton).toBeVisible();
   74 |
   75 |     // Click somewhere to deselect
   76 |     await editor.click();
   77 |
   78 |     // Formatting toolbar and button should be hidden again
   79 |     await expect(addCommentButton).not.toBeVisible();
   80 |   });
   81 |
   82 |   test('should open comment sidebar and display comments', async ({ page }) => {
   83 |     // Assuming we have a document with comments from previous test
   84 |     // or we create one here
   85 |     
   86 |     // Click the comment toggle button in toolbar
   87 |     await page.click('button[title="Toggle Comments"]');
   88 |     
   89 |     // Verify sidebar opens
   90 |     await page.waitForSelector('[data-testid="comment-sidebar"]', { timeout: 3000 });
   91 |     
   92 |     // Check that sidebar is visible
   93 |     const sidebar = page.locator('[data-testid="comment-sidebar"]');
   94 |     await expect(sidebar).toBeVisible();
   95 |     
   96 |     // Verify sidebar shows comment count
   97 |     await expect(page.locator('text=Comments')).toBeVisible();
   98 |   });
   99 |
  100 |   test('should reply to a comment', async ({ page }) => {
  101 |     // Navigate to a document with existing comments
  102 |     // This would need to be set up in test data
  103 |     
  104 |     // Open comment sidebar
  105 |     await page.click('button[title="Toggle Comments"]');
  106 |     await page.waitForSelector('[data-testid="comment-sidebar"]');
  107 |     
  108 |     // Click on a comment to expand it
  109 |     const firstComment = page.locator('[data-testid="comment-thread"]').first();
  110 |     await firstComment.click();
  111 |     
  112 |     // Click reply button
  113 |     await page.click('button:has-text("Reply")');
  114 |     
  115 |     // Type reply content
  116 |     const replyInput = page.locator('textarea[placeholder*="reply"]');
  117 |     await replyInput.fill('This is a reply to the comment.');
  118 |     
  119 |     // Submit reply
  120 |     await page.click('button:has-text("Reply")');
  121 |     
  122 |     // Verify reply appears
  123 |     await page.waitForSelector('[data-testid="comment-reply"]', { timeout: 3000 });
  124 |     expect(await page.locator('[data-testid="comment-reply"]').count()).toBeGreaterThan(0);
  125 |   });
  126 |
  127 |   test('should edit own comment', async ({ page }) => {
  128 |     // Open comment sidebar
  129 |     await page.click('button[title="Toggle Comments"]');
  130 |     await page.waitForSelector('[data-testid="comment-sidebar"]');
  131 |     
  132 |     // Click on comment options menu
  133 |     const commentOptions = page.locator('[data-testid="comment-options"]').first();
  134 |     await commentOptions.click();
  135 |     
  136 |     // Click edit option
  137 |     await page.click('text=Edit');
  138 |     
  139 |     // Modify comment content
  140 |     const editInput = page.locator('textarea[value*="test comment"]');
  141 |     await editInput.clear();
  142 |     await editInput.fill('This is an edited comment.');
  143 |     
  144 |     // Submit edit
  145 |     await page.click('button:has-text("Update")');
  146 |     
  147 |     // Verify comment was updated
  148 |     await expect(page.locator('text=This is an edited comment.')).toBeVisible();
  149 |   });
  150 |
  151 |   test('should delete own comment', async ({ page }) => {
  152 |     // Open comment sidebar
  153 |     await page.click('button[title="Toggle Comments"]');
  154 |     await page.waitForSelector('[data-testid="comment-sidebar"]');
  155 |     
  156 |     // Get initial comment count
  157 |     const initialCount = await page.locator('[data-testid="comment-thread"]').count();
```